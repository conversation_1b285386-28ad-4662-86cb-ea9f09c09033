# 依赖文件
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js构建文件
.next/
out/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs
*.log

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 上传文件
public/uploads/*

# IDE文件
.vscode/
.idea/
*.swp
*.swo

# Git文件
.git
.gitignore
README.md

# 测试文件
coverage/
.nyc_output/

# Docker文件
Dockerfile*
docker-compose*
.dockerignore

# 操作系统文件
.DS_Store
Thumbs.db

# 临时文件
tmp/
temp/