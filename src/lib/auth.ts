import { NextAuthOptions } from 'next-auth'
import Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'
import bcrypt from 'bcryptjs'
import { prisma } from '@/lib/prisma'
import type { User } from '@/types'

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        username: { label: '用户名', type: 'text' },
        password: { label: '密码', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.username || !credentials?.password) {
          return null
        }

        try {
          // 查找用户
          const user = await prisma.user.findUnique({
            where: {
              username: credentials.username,
            },
          })

          if (!user || !user.isActive) {
            return null
          }

          // 验证密码
          const isValidPassword = await bcrypt.compare(
            credentials.password,
            user.passwordHash
          )

          if (!isValidPassword) {
            return null
          }

          // 更新最后登录时间和登录次数
          await prisma.user.update({
            where: { id: user.id },
            data: { 
              lastLoginAt: new Date(),
              loginCount: { increment: 1 }
            },
          })

          // 记录登录日志
          await prisma.systemLog.create({
            data: {
              userId: user.id,
              action: 'LOGIN',
              resource: 'User',
              resourceId: user.id.toString(),
              details: {
                username: user.username,
                nickname: user.nickname,
              },
              ipAddress: 'unknown',
              userAgent: 'unknown',
            },
          })

          // 返回用户信息（不包含密码）
          return {
            id: user.id.toString(),
            username: user.username,
            nickname: user.nickname,
            email: user.email,
            avatar: user.avatar,
            role: user.role,
            isActive: user.isActive,
          }
        } catch (error) {
          console.error('认证错误:', error)
          return null
        }
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 天
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 天
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
        token.username = user.username
        token.nickname = user.nickname
        token.email = user.email
        token.avatarUrl = user.avatarUrl
        token.isActive = user.isActive
      }
      return token
    },
    async session({ session, token }) {
      session.user = {
        id: Number(token.id),
        username: token.username as string,
        nickname: token.nickname as string,
        email: token.email as string,
        avatarUrl: token.avatarUrl as string,
        isActive: token.isActive as boolean,
      }
      return session
    },
    async redirect({ url, baseUrl }) {
      // 登录后重定向到仪表板
      if (url.startsWith('/')) return `${baseUrl}${url}`
      if (new URL(url).origin === baseUrl) return url
      return `${baseUrl}/dashboard`
    },
  },
  pages: {
    signIn: '/login',
    error: '/login',
  },
  secret: process.env.NEXTAUTH_SECRET,
  debug: process.env.NODE_ENV === 'development',
}