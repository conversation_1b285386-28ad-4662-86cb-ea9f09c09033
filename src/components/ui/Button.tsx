'use client'

import { <PERSON><PERSON> as Ant<PERSON>utton, ButtonProps as AntButtonProps } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'
import { cn } from '@/lib/utils'

interface ButtonProps extends Omit<AntButtonProps, 'loading'> {
  loading?: boolean
  loadingText?: string
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
  fullWidth?: boolean
}

export function Button({
  children,
  className,
  loading = false,
  loadingText,
  variant = 'primary',
  fullWidth = false,
  disabled,
  ...props
}: ButtonProps) {
  // 根据 variant 设置 Ant Design 的 type
  const getAntType = () => {
    switch (variant) {
      case 'primary':
        return 'primary'
      case 'danger':
        return 'primary'
      case 'outline':
        return 'default'
      case 'ghost':
        return 'text'
      case 'secondary':
      default:
        return 'default'
    }
  }

  // 根据 variant 设置额外的样式类
  const getVariantClass = () => {
    switch (variant) {
      case 'danger':
        return 'bg-red-500 border-red-500 hover:bg-red-600 hover:border-red-600'
      case 'outline':
        return 'border-primary-500 text-primary-500 hover:bg-primary-50'
      case 'ghost':
        return 'text-primary-500 hover:bg-primary-50'
      case 'secondary':
        return 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
      default:
        return ''
    }
  }

  return (
    <AntButton
      {...props}
      type={getAntType()}
      disabled={disabled || loading}
      loading={loading}
      className={cn(
        'flex items-center justify-center transition-all duration-200',
        getVariantClass(),
        fullWidth && 'w-full',
        className
      )}
      icon={loading ? <LoadingOutlined /> : props.icon}
    >
      {loading && loadingText ? loadingText : children}
    </AntButton>
  )
}

// 预设的按钮组合
export function PrimaryButton(props: Omit<ButtonProps, 'variant'>) {
  return <Button {...props} variant="primary" />
}

export function SecondaryButton(props: Omit<ButtonProps, 'variant'>) {
  return <Button {...props} variant="secondary" />
}

export function OutlineButton(props: Omit<ButtonProps, 'variant'>) {
  return <Button {...props} variant="outline" />
}

export function GhostButton(props: Omit<ButtonProps, 'variant'>) {
  return <Button {...props} variant="ghost" />
}

export function DangerButton(props: Omit<ButtonProps, 'variant'>) {
  return <Button {...props} variant="danger" />
}

// 按钮组
interface ButtonGroupProps {
  children: React.ReactNode
  className?: string
  size?: 'small' | 'middle' | 'large'
}

export function ButtonGroup({ children, className, size = 'middle' }: ButtonGroupProps) {
  return (
    <AntButton.Group size={size} className={cn('flex', className)}>
      {children}
    </AntButton.Group>
  )
}