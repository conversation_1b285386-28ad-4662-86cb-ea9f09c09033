import NextAuth from 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      id: number
      username: string
      nickname?: string
      email?: string
      avatarUrl?: string
      isActive: boolean
    }
  }

  interface User {
    id: string
    username: string
    nickname?: string
    email?: string
    avatarUrl?: string
    isActive: boolean
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string
    username: string
    nickname?: string
    email?: string
    avatarUrl?: string
    isActive: boolean
  }
}