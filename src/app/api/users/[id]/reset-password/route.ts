import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

// 生成随机密码
function generateRandomPassword(): string {
  const length = 12
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*'
  let password = ''
  
  // 确保包含各种类型的字符
  password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)] // 小写字母
  password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)] // 大写字母
  password += '0123456789'[Math.floor(Math.random() * 10)] // 数字
  password += '!@#$%^&*'[Math.floor(Math.random() * 8)] // 特殊字符
  
  // 生成剩余字符
  for (let i = 4; i < length; i++) {
    password += charset[Math.floor(Math.random() * charset.length)]
  }
  
  // 随机打乱字符顺序
  return password.split('').sort(() => Math.random() - 0.5).join('')
}

// POST /api/users/[id]/reset-password - 重置用户密码
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    // 检查是否为管理员
    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(session.user.id!) },
      select: { role: true }
    })

    if (currentUser?.role !== 'admin') {
      return NextResponse.json({ error: '权限不足' }, { status: 403 })
    }

    const userId = parseInt(params.id)

    // 不能重置自己的密码
    if (parseInt(session.user.id!) === userId) {
      return NextResponse.json({
        success: false,
        error: '不能重置自己的密码，请使用修改密码功能',
      }, { status: 400 })
    }

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, username: true, nickname: true, isActive: true }
    })

    if (!existingUser) {
      return NextResponse.json(
        { success: false, error: '用户不存在' },
        { status: 404 }
      )
    }

    if (!existingUser.isActive) {
      return NextResponse.json({
        success: false,
        error: '不能重置已禁用用户的密码',
      }, { status: 400 })
    }

    // 生成新的随机密码
    const newPassword = generateRandomPassword()
    const saltRounds = 12
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds)

    // 更新用户密码
    await prisma.user.update({
      where: { id: userId },
      data: {
        passwordHash: newPasswordHash,
        updatedAt: new Date(),
      },
    })

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: parseInt(session.user.id!),
        action: 'RESET_USER_PASSWORD',
        resource: 'User',
        resourceId: userId.toString(),
        details: {
          targetUser: existingUser.username,
          targetNickname: existingUser.nickname,
          resetBy: session.user.username,
          timestamp: new Date().toISOString(),
        },
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    return NextResponse.json({
      success: true,
      message: '密码重置成功',
      data: {
        newPassword,
        username: existingUser.username,
        nickname: existingUser.nickname,
      },
    })

  } catch (error) {
    console.error('重置密码失败:', error)
    return NextResponse.json(
      { success: false, error: '重置密码失败' },
      { status: 500 }
    )
  }
}