import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// 更新用户验证schema
const updateUserSchema = z.object({
  nickname: z.string().min(1, '昵称不能为空').max(50, '昵称不能超过50个字符').optional(),
  email: z.string().email('请输入有效的邮箱地址').max(100, '邮箱地址不能超过100个字符').optional().or(z.literal('')),
  role: z.enum(['user', 'admin']).optional(),
  isActive: z.boolean().optional(),
})

// GET /api/users/[id] - 获取单个用户详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const userId = parseInt(params.id)

    // 检查权限：管理员可以查看所有用户，普通用户只能查看自己
    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(session.user.id!) },
      select: { role: true }
    })

    if (currentUser?.role !== 'admin' && parseInt(session.user.id!) !== userId) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 })
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        nickname: true,
        email: true,
        avatar: true,
        isActive: true,
        role: true,
        createdAt: true,
        lastLoginAt: true,
        loginCount: true,
      }
    })

    if (!user) {
      return NextResponse.json(
        { success: false, error: '用户不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        ...user,
        id: user.id.toString(),
      },
    })
  } catch (error) {
    console.error('获取用户详情失败:', error)
    return NextResponse.json(
      { success: false, error: '获取用户详情失败' },
      { status: 500 }
    )
  }
}

// PUT /api/users/[id] - 更新用户
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const userId = parseInt(params.id)
    const body = await request.json()

    // 检查权限：管理员可以修改所有用户，普通用户只能修改自己的部分信息
    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(session.user.id!) },
      select: { role: true }
    })

    const isAdmin = currentUser?.role === 'admin'
    const isSelf = parseInt(session.user.id!) === userId

    if (!isAdmin && !isSelf) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 })
    }

    // 验证请求数据
    const validation = updateUserSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: '数据验证失败',
        details: validation.error.issues,
      }, { status: 400 })
    }

    let updateData = validation.data

    // 普通用户不能修改角色和状态
    if (!isAdmin) {
      const { role, isActive, ...allowedData } = updateData
      updateData = allowedData
    }

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!existingUser) {
      return NextResponse.json(
        { success: false, error: '用户不存在' },
        { status: 404 }
      )
    }

    // 检查邮箱是否已被其他用户使用
    if (updateData.email && updateData.email !== existingUser.email) {
      const existingEmail = await prisma.user.findFirst({
        where: { 
          email: updateData.email,
          id: { not: userId }
        }
      })

      if (existingEmail) {
        return NextResponse.json({
          success: false,
          error: '邮箱已被其他用户使用',
        }, { status: 409 })
      }
    }

    // 更新用户
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        ...updateData,
        email: updateData.email || null,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        username: true,
        nickname: true,
        email: true,
        avatar: true,
        isActive: true,
        role: true,
        updatedAt: true,
      }
    })

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: parseInt(session.user.id!),
        action: 'UPDATE_USER',
        resource: 'User',
        resourceId: userId.toString(),
        details: {
          updatedFields: Object.keys(updateData),
          targetUser: existingUser.username,
          updatedBy: session.user.username,
        },
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    return NextResponse.json({
      success: true,
      message: '用户更新成功',
      data: {
        ...updatedUser,
        id: updatedUser.id.toString(),
      },
    })

  } catch (error) {
    console.error('更新用户失败:', error)
    return NextResponse.json(
      { success: false, error: '更新用户失败' },
      { status: 500 }
    )
  }
}

// DELETE /api/users/[id] - 删除用户
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    // 检查是否为管理员
    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(session.user.id!) },
      select: { role: true }
    })

    if (currentUser?.role !== 'admin') {
      return NextResponse.json({ error: '权限不足' }, { status: 403 })
    }

    const userId = parseInt(params.id)

    // 不能删除自己
    if (parseInt(session.user.id!) === userId) {
      return NextResponse.json({
        success: false,
        error: '不能删除自己的账户',
      }, { status: 400 })
    }

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, username: true, role: true }
    })

    if (!existingUser) {
      return NextResponse.json(
        { success: false, error: '用户不存在' },
        { status: 404 }
      )
    }

    // 软删除：标记为非活跃状态
    await prisma.user.update({
      where: { id: userId },
      data: {
        isActive: false,
        updatedAt: new Date(),
      },
    })

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: parseInt(session.user.id!),
        action: 'DELETE_USER',
        resource: 'User',
        resourceId: userId.toString(),
        details: {
          deletedUser: existingUser.username,
          deletedUserRole: existingUser.role,
          deletedBy: session.user.username,
        },
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    return NextResponse.json({
      success: true,
      message: '用户删除成功',
    })

  } catch (error) {
    console.error('删除用户失败:', error)
    return NextResponse.json(
      { success: false, error: '删除用户失败' },
      { status: 500 }
    )
  }
}