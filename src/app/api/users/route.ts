import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import { z } from 'zod'

// 创建用户验证schema
const createUserSchema = z.object({
  username: z.string()
    .min(3, '用户名至少3个字符')
    .max(20, '用户名不能超过20个字符')
    .regex(/^[a-zA-Z0-9_-]+$/, '用户名只能包含字母、数字、下划线和横线'),
  nickname: z.string().min(1, '昵称不能为空').max(50, '昵称不能超过50个字符'),
  email: z.string().email('请输入有效的邮箱地址').max(100, '邮箱地址不能超过100个字符').optional().or(z.literal('')),
  role: z.enum(['user', 'admin']).default('user'),
})

// 生成随机密码
function generateRandomPassword(): string {
  const length = 12
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*'
  let password = ''
  
  // 确保包含各种类型的字符
  password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)] // 小写字母
  password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)] // 大写字母
  password += '0123456789'[Math.floor(Math.random() * 10)] // 数字
  password += '!@#$%^&*'[Math.floor(Math.random() * 8)] // 特殊字符
  
  // 生成剩余字符
  for (let i = 4; i < length; i++) {
    password += charset[Math.floor(Math.random() * charset.length)]
  }
  
  // 随机打乱字符顺序
  return password.split('').sort(() => Math.random() - 0.5).join('')
}

// GET /api/users - 获取用户列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    // 检查是否为管理员
    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(session.user.id!) },
      select: { role: true }
    })

    if (currentUser?.role !== 'admin') {
      return NextResponse.json({ error: '权限不足' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''

    const skip = (page - 1) * limit

    // 构建查询条件
    const where = search ? {
      OR: [
        { username: { contains: search, mode: 'insensitive' as const } },
        { nickname: { contains: search, mode: 'insensitive' as const } },
        { email: { contains: search, mode: 'insensitive' as const } },
      ]
    } : {}

    // 获取总数
    const total = await prisma.user.count({ where })

    // 获取用户列表
    const users = await prisma.user.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        username: true,
        nickname: true,
        email: true,
        avatar: true,
        isActive: true,
        role: true,
        createdAt: true,
        lastLoginAt: true,
        loginCount: true,
      },
    })

    return NextResponse.json({
      success: true,
      data: {
        users: users.map(user => ({
          ...user,
          id: user.id.toString(),
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    })
  } catch (error) {
    console.error('获取用户列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取用户列表失败' },
      { status: 500 }
    )
  }
}

// POST /api/users - 创建用户
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    // 检查是否为管理员
    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(session.user.id!) },
      select: { role: true }
    })

    if (currentUser?.role !== 'admin') {
      return NextResponse.json({ error: '权限不足' }, { status: 403 })
    }

    const body = await request.json()
    
    // 验证请求数据
    const validation = createUserSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: '数据验证失败',
        details: validation.error.issues,
      }, { status: 400 })
    }

    const { username, nickname, email, role } = validation.data

    // 检查用户名是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { username }
    })

    if (existingUser) {
      return NextResponse.json({
        success: false,
        error: '用户名已存在',
      }, { status: 409 })
    }

    // 检查邮箱是否已存在（如果提供了邮箱）
    if (email) {
      const existingEmail = await prisma.user.findFirst({
        where: { email }
      })

      if (existingEmail) {
        return NextResponse.json({
          success: false,
          error: '邮箱已被使用',
        }, { status: 409 })
      }
    }

    // 生成随机密码
    const randomPassword = generateRandomPassword()
    const saltRounds = 12
    const passwordHash = await bcrypt.hash(randomPassword, saltRounds)

    // 创建用户
    const newUser = await prisma.user.create({
      data: {
        username,
        nickname,
        email: email || null,
        passwordHash,
        role,
        isActive: true,
      },
      select: {
        id: true,
        username: true,
        nickname: true,
        email: true,
        role: true,
        isActive: true,
        createdAt: true,
      }
    })

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        userId: parseInt(session.user.id!),
        action: 'CREATE_USER',
        resource: 'User',
        resourceId: newUser.id.toString(),
        details: {
          username,
          nickname,
          role,
          createdBy: session.user.username,
        },
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    return NextResponse.json({
      success: true,
      message: '用户创建成功',
      data: {
        user: {
          ...newUser,
          id: newUser.id.toString(),
        },
        initialPassword: randomPassword,
      },
    }, { status: 201 })

  } catch (error) {
    console.error('创建用户失败:', error)
    return NextResponse.json(
      { success: false, error: '创建用户失败' },
      { status: 500 }
    )
  }
}