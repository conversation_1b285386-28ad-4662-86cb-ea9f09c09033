import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/data/[formId] - 获取表单数据
export async function GET(
  request: NextRequest,
  { params }: { params: { formId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const formId = params.formId
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    // 获取表单配置
    const formConfig = await prisma.formConfig.findUnique({
      where: { formId, isActive: true }
    })

    if (!formConfig) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在或已禁用' },
        { status: 404 }
      )
    }

    const tableName = formConfig.tableName
    const skip = (page - 1) * limit

    // 构建搜索条件
    let whereConditions: string[] = []
    let queryParams: any[] = []
    let paramIndex = 1

    // 处理搜索参数
    for (const [key, value] of searchParams.entries()) {
      if (key === 'page' || key === 'limit') continue
      
      if (key.endsWith('_start')) {
        const fieldName = key.replace('_start', '')
        whereConditions.push(`\`${fieldName}\` >= ?`)
        queryParams.push(value)
      } else if (key.endsWith('_end')) {
        const fieldName = key.replace('_end', '')
        whereConditions.push(`\`${fieldName}\` <= ?`)
        queryParams.push(value + ' 23:59:59')
      } else if (key === 'serial_number') {
        whereConditions.push('serial_number = ?')
        queryParams.push(parseInt(value))
      } else {
        // 字符串字段模糊搜索
        whereConditions.push(`\`${key}\` LIKE ?`)
        queryParams.push(`%${value}%`)
      }
    }

    const whereClause = whereConditions.length > 0 ? 
      `WHERE ${whereConditions.join(' AND ')}` : ''

    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM \`${tableName}\` ${whereClause}`
    const countResult = await prisma.$queryRawUnsafe<Array<{ total: number }>>(
      countQuery,
      ...queryParams
    )
    const total = countResult[0]?.total || 0

    // 获取数据
    const dataQuery = `
      SELECT * FROM \`${tableName}\` 
      ${whereClause}
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `
    
    const records = await prisma.$queryRawUnsafe<Array<any>>(
      dataQuery,
      ...queryParams,
      limit,
      skip
    )

    // 转换数据格式
    const formattedRecords = records.map(record => ({
      ...record,
      id: record.id.toString(),
      created_at: record.created_at.toISOString(),
      updated_at: record.updated_at?.toISOString(),
      raw_data: typeof record.raw_data === 'string' ? 
        JSON.parse(record.raw_data) : record.raw_data,
    }))

    return NextResponse.json({
      success: true,
      data: {
        records: formattedRecords,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        formConfig: {
          formId: formConfig.formId,
          formName: formConfig.formName,
          fieldMapping: formConfig.fieldMapping,
        },
      },
    })

  } catch (error) {
    console.error('获取表单数据失败:', error)
    return NextResponse.json(
      { success: false, error: '获取表单数据失败' },
      { status: 500 }
    )
  }
}