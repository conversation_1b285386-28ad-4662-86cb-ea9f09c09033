import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import * as XLSX from 'xlsx'

// GET /api/data/[formId]/export - 导出数据
export async function GET(
  request: NextRequest,
  { params }: { params: { formId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const formId = params.formId
    const { searchParams } = new URL(request.url)
    const format = searchParams.get('format') || 'excel'
    const idsParam = searchParams.get('ids')
    const specificIds = idsParam ? idsParam.split(',') : null

    // 获取表单配置
    const formConfig = await prisma.formConfig.findUnique({
      where: { formId, isActive: true }
    })

    if (!formConfig) {
      return NextResponse.json(
        { success: false, error: '表单配置不存在或已禁用' },
        { status: 404 }
      )
    }

    const tableName = formConfig.tableName
    const fieldMapping = formConfig.fieldMapping as Record<string, any>

    // 构建查询条件
    let whereConditions: string[] = []
    let queryParams: any[] = []

    // 如果指定了特定ID，只导出这些数据
    if (specificIds && specificIds.length > 0) {
      const placeholders = specificIds.map(() => '?').join(',')
      whereConditions.push(`id IN (${placeholders})`)
      queryParams.push(...specificIds.map(id => parseInt(id)))
    } else {
      // 处理搜索参数
      for (const [key, value] of searchParams.entries()) {
        if (['format', 'ids'].includes(key)) continue
        
        if (key.endsWith('_start')) {
          const fieldName = key.replace('_start', '')
          whereConditions.push(`\`${fieldName}\` >= ?`)
          queryParams.push(value)
        } else if (key.endsWith('_end')) {
          const fieldName = key.replace('_end', '')
          whereConditions.push(`\`${fieldName}\` <= ?`)
          queryParams.push(value + ' 23:59:59')
        } else if (key === 'serial_number') {
          whereConditions.push('serial_number = ?')
          queryParams.push(parseInt(value))
        } else if (Object.keys(fieldMapping).includes(key)) {
          whereConditions.push(`\`${key}\` LIKE ?`)
          queryParams.push(`%${value}%`)
        }
      }
    }

    const whereClause = whereConditions.length > 0 ? 
      `WHERE ${whereConditions.join(' AND ')}` : ''

    // 获取数据
    const dataQuery = `
      SELECT * FROM \`${tableName}\` 
      ${whereClause}
      ORDER BY created_at DESC
    `
    
    const records = await prisma.$queryRawUnsafe<Array<any>>(
      dataQuery,
      ...queryParams
    )

    if (records.length === 0) {
      return NextResponse.json(
        { success: false, error: '没有数据可导出' },
        { status: 400 }
      )
    }

    // 准备导出数据
    const exportData = records.map((record, index) => {
      const row: any = {
        '序号': index + 1,
        '金数据序号': record.serial_number,
      }

      // 添加字段数据
      Object.entries(fieldMapping).forEach(([key, config]) => {
        const value = record[key]
        row[config.name] = value === null || value === undefined ? '' : 
                           typeof value === 'object' ? JSON.stringify(value) : 
                           String(value)
      })

      row['创建时间'] = record.created_at?.toLocaleString('zh-CN') || ''
      row['更新时间'] = record.updated_at?.toLocaleString('zh-CN') || ''
      row['来源IP'] = record.source_ip || ''
      row['创建者'] = record.creator_name || ''

      return row
    })

    // 记录导出日志
    await prisma.systemLog.create({
      data: {
        userId: parseInt(session.user.id!),
        action: 'EXPORT_DATA',
        resource: 'FormData',
        resourceId: formId,
        details: {
          formId,
          format,
          recordCount: records.length,
          isFiltered: whereConditions.length > 0,
          isPartial: !!specificIds,
        },
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    })

    if (format === 'csv') {
      // 生成CSV
      const headers = Object.keys(exportData[0])
      const csvContent = [
        headers.join(','),
        ...exportData.map(row => 
          headers.map(header => {
            const value = row[header]
            // 处理包含逗号、引号或换行符的值
            if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
              return `"${value.replace(/"/g, '""')}"`
            }
            return value
          }).join(',')
        )
      ].join('\n')

      // 添加BOM以支持Excel正确显示中文
      const bom = '\uFEFF'
      const csvBuffer = Buffer.from(bom + csvContent, 'utf8')

      return new NextResponse(csvBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv; charset=utf-8',
          'Content-Disposition': `attachment; filename="${formConfig.formName}_${new Date().toISOString().split('T')[0]}.csv"`,
        },
      })
    } else {
      // 生成Excel
      const workbook = XLSX.utils.book_new()
      const worksheet = XLSX.utils.json_to_sheet(exportData)

      // 设置列宽
      const colWidths = Object.keys(exportData[0]).map(key => ({
        wch: Math.max(key.length, 15)
      }))
      worksheet['!cols'] = colWidths

      XLSX.utils.book_append_sheet(workbook, worksheet, '数据导出')

      // 生成Excel文件
      const excelBuffer = XLSX.write(workbook, {
        type: 'buffer',
        bookType: 'xlsx'
      })

      return new NextResponse(excelBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': `attachment; filename="${formConfig.formName}_${new Date().toISOString().split('T')[0]}.xlsx"`,
        },
      })
    }

  } catch (error) {
    console.error('导出数据失败:', error)
    return NextResponse.json(
      { success: false, error: '导出数据失败' },
      { status: 500 }
    )
  }
}