'use client'

import { useState, useEffect } from 'react'
import { Card, Form, Select, Upload, Button, Space, message, Table, Modal, Progress } from 'antd'
import { UploadOutlined, DeleteOutlined, EditOutlined, DownloadOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { UploadProps } from 'antd'

const { Option } = Select
const { Dragger } = Upload

interface FormConfig {
  id: string
  name: string
  description: string
}

interface BatchOperation {
  id: string
  type: 'import' | 'update' | 'delete'
  formId: string
  formName: string
  fileName: string
  totalRecords: number
  processedRecords: number
  successRecords: number
  failedRecords: number
  status: 'pending' | 'processing' | 'completed' | 'failed'
  createdAt: string
  completedAt?: string
  errorMessage?: string
}

export default function BatchOperationsPage() {
  const [forms, setForms] = useState<FormConfig[]>([])
  const [operations, setOperations] = useState<BatchOperation[]>([])
  const [loading, setLoading] = useState(false)
  const [operationsLoading, setOperationsLoading] = useState(true)
  const [selectedFormId, setSelectedFormId] = useState<string>()
  const [uploadModalVisible, setUploadModalVisible] = useState(false)
  const [form] = Form.useForm()

  useEffect(() => {
    fetchForms()
    fetchOperations()
  }, [])

  const fetchForms = async () => {
    try {
      const response = await fetch('/api/forms')
      if (response.ok) {
        const data = await response.json()
        setForms(data)
      } else {
        message.error('获取表单列表失败')
      }
    } catch (error) {
      console.error('获取表单列表失败:', error)
      message.error('获取表单列表失败')
    }
  }

  const fetchOperations = async () => {
    try {
      setOperationsLoading(true)
      // TODO: 实现批量操作历史 API
      // const response = await fetch('/api/data/batch/operations')
      // if (response.ok) {
      //   const data = await response.json()
      //   setOperations(data)
      // }
      // 模拟数据
      setOperations([
        {
          id: '1',
          type: 'import',
          formId: 'form1',
          formName: '肺功能检查表',
          fileName: 'lung_function_data.xlsx',
          totalRecords: 100,
          processedRecords: 100,
          successRecords: 95,
          failedRecords: 5,
          status: 'completed',
          createdAt: '2024-01-15 10:30:00',
          completedAt: '2024-01-15 10:35:00'
        }
      ])
    } catch (error) {
      console.error('获取操作历史失败:', error)
      message.error('获取操作历史失败')
    } finally {
      setOperationsLoading(false)
    }
  }

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: '.xlsx,.xls,.csv',
    beforeUpload: (file) => {
      if (!selectedFormId) {
        message.error('请先选择目标表单')
        return false
      }
      return true
    },
    customRequest: async ({ file, onProgress, onSuccess, onError }) => {
      try {
        const formData = new FormData()
        formData.append('file', file as File)
        formData.append('formId', selectedFormId!)

        const response = await fetch('/api/data/batch/import', {
          method: 'POST',
          body: formData,
        })

        if (response.ok) {
          const result = await response.json()
          onSuccess?.(result)
          message.success('文件上传成功，开始处理数据')
          fetchOperations()
          setUploadModalVisible(false)
        } else {
          const error = await response.text()
          onError?.(new Error(error))
          message.error('文件上传失败')
        }
      } catch (error) {
        onError?.(error as Error)
        message.error('文件上传失败')
      }
    },
    onDrop(e) {
      console.log('Dropped files', e.dataTransfer.files)
    },
  }

  const handleBatchDelete = () => {
    Modal.confirm({
      title: '批量删除确认',
      content: '此操作将删除选中表单的所有数据，操作不可恢复。确定要继续吗？',
      okText: '确定删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true)
          const response = await fetch(`/api/data/batch/delete`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ formId: selectedFormId }),
          })

          if (response.ok) {
            message.success('批量删除操作已开始')
            fetchOperations()
          } else {
            message.error('批量删除失败')
          }
        } catch (error) {
          console.error('批量删除失败:', error)
          message.error('批量删除失败')
        } finally {
          setLoading(false)
        }
      },
    })
  }

  const downloadTemplate = () => {
    if (!selectedFormId) {
      message.error('请先选择表单')
      return
    }
    
    const url = `/api/data/batch/template?formId=${selectedFormId}`
    window.open(url)
  }

  const columns: ColumnsType<BatchOperation> = [
    {
      title: '操作类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => {
        const typeMap = {
          import: { color: 'blue', text: '数据导入' },
          update: { color: 'orange', text: '批量更新' },
          delete: { color: 'red', text: '批量删除' }
        }
        const config = typeMap[type as keyof typeof typeMap]
        return <span style={{ color: config.color }}>{config.text}</span>
      },
    },
    {
      title: '表单名称',
      dataIndex: 'formName',
      key: 'formName',
    },
    {
      title: '文件名',
      dataIndex: 'fileName',
      key: 'fileName',
      render: (fileName) => fileName || '-',
    },
    {
      title: '进度',
      key: 'progress',
      render: (_, record) => {
        const percentage = record.totalRecords > 0 
          ? Math.round((record.processedRecords / record.totalRecords) * 100)
          : 0
        
        return (
          <div className="space-y-1">
            <Progress 
              percent={percentage} 
              size="small"
              status={record.status === 'failed' ? 'exception' : undefined}
            />
            <div className="text-xs text-gray-500">
              {record.processedRecords}/{record.totalRecords} 
              {record.status === 'completed' && (
                <span className="ml-2">
                  成功: {record.successRecords}, 失败: {record.failedRecords}
                </span>
              )}
            </div>
          </div>
        )
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          pending: { color: 'gray', text: '等待中' },
          processing: { color: 'blue', text: '处理中' },
          completed: { color: 'green', text: '已完成' },
          failed: { color: 'red', text: '失败' }
        }
        const config = statusMap[status as keyof typeof statusMap]
        return <span style={{ color: config.color }}>{config.text}</span>
      },
    },
    {
      title: '开始时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '完成时间',
      dataIndex: 'completedAt',
      key: 'completedAt',
      render: (time) => time || '-',
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          {record.status === 'failed' && record.errorMessage && (
            <Button
              type="text"
              size="small"
              onClick={() => {
                Modal.info({
                  title: '错误详情',
                  content: record.errorMessage,
                })
              }}
            >
              查看错误
            </Button>
          )}
          {record.failedRecords > 0 && (
            <Button
              type="text"
              size="small"
              icon={<DownloadOutlined />}
              onClick={() => {
                window.open(`/api/data/batch/errors?operationId=${record.id}`)
              }}
            >
              下载错误报告
            </Button>
          )}
        </Space>
      ),
    },
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">批量操作</h1>
        <p className="text-gray-600 mt-1">批量导入、更新或删除表单数据</p>
      </div>

      <Card title="操作面板">
        <div className="space-y-4">
          <Form.Item label="选择表单">
            <Select
              placeholder="请选择要操作的表单"
              value={selectedFormId}
              onChange={setSelectedFormId}
              className="w-full max-w-md"
            >
              {forms.map(form => (
                <Option key={form.id} value={form.id}>
                  {form.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Space wrap>
            <Button
              type="primary"
              icon={<UploadOutlined />}
              onClick={() => setUploadModalVisible(true)}
              disabled={!selectedFormId}
            >
              批量导入
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={downloadTemplate}
              disabled={!selectedFormId}
            >
              下载模板
            </Button>
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={handleBatchDelete}
              disabled={!selectedFormId}
              loading={loading}
            >
              批量删除
            </Button>
          </Space>
        </div>
      </Card>

      <Card title="操作历史">
        <Table
          columns={columns}
          dataSource={operations}
          loading={operationsLoading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条操作记录`,
          }}
        />
      </Card>

      <Modal
        title="批量导入数据"
        open={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        footer={null}
        width={600}
      >
        <div className="space-y-4">
          <div className="text-sm text-gray-600">
            <p>请上传 Excel 或 CSV 格式的文件，文件格式必须与表单字段匹配。</p>
            <p>建议先下载模板文件，按照模板格式准备数据。</p>
          </div>
          
          <Dragger {...uploadProps}>
            <p className="ant-upload-drag-icon">
              <UploadOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">
              支持 .xlsx, .xls, .csv 格式文件
            </p>
          </Dragger>
        </div>
      </Modal>
    </div>
  )
}