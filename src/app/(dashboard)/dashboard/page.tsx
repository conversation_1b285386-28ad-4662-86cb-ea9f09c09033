'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  Typography,
  Row,
  Col,
  Statistic,
  Table,
  List,
  Progress,
  Tag,
  Space,
  Button,
  Alert,
} from 'antd'
import {
  UserOutlined,
  FormOutlined,
  DatabaseOutlined,
  LinkOutlined,
  TrophyOutlined,
  RiseOutlined,
  Clock<PERSON>ircleOutlined,
  CheckCircleOutlined,
  Exclamation<PERSON>ircleOutlined,
  ReloadOutlined,
} from '@ant-design/icons'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'
import type { ColumnsType } from 'antd/es/table'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

const { Title, Text } = Typography

interface DashboardStats {
  totalUsers: number
  activeUsers: number
  totalForms: number
  activeForms: number
  totalRecords: number
  todayRecords: number
  webhookSuccess: number
  webhookErrors: number
}

interface RecentActivity {
  id: string
  action: string
  resource: string
  details: any
  createdAt: string
  user?: {
    username: string
    nickname: string
  }
}

interface FormStats {
  formId: string
  formName: string
  recordCount: number
  todayCount: number
  isActive: boolean
}

export default function DashboardPage() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    activeUsers: 0,
    totalForms: 0,
    activeForms: 0,
    totalRecords: 0,
    todayRecords: 0,
    webhookSuccess: 0,
    webhookErrors: 0,
  })
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([])
  const [formStats, setFormStats] = useState<FormStats[]>([])
  const [loading, setLoading] = useState(false)

  // 获取仪表板数据
  const fetchDashboardData = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/dashboard')
      const result = await response.json()
      
      if (result.success) {
        setStats(result.data.stats)
        setRecentActivities(result.data.recentActivities)
        setFormStats(result.data.formStats)
      }
    } catch (error) {
      console.error('获取仪表板数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDashboardData()
  }, [])

  // 操作类型映射
  const actionMap: Record<string, { label: string; color: string }> = {
    'LOGIN': { label: '用户登录', color: 'green' },
    'CREATE_USER': { label: '创建用户', color: 'cyan' },
    'CREATE_FORM_CONFIG': { label: '创建表单配置', color: 'blue' },
    'WEBHOOK_RECEIVED': { label: 'Webhook接收', color: 'lime' },
    'WEBHOOK_ERROR': { label: 'Webhook错误', color: 'red' },
    'BATCH_DELETE_DATA': { label: '批量删除数据', color: 'red' },
    'EXPORT_DATA': { label: '导出数据', color: 'orange' },
  }

  // 表单统计表格列
  const formColumns: ColumnsType<FormStats> = [
    {
      title: '表单名称',
      dataIndex: 'formName',
      ellipsis: true,
      render: (name, record) => (
        <Link href={`/data/view?form=${record.formId}`}>
          {name}
        </Link>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      width: 80,
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '活跃' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '总数据',
      dataIndex: 'recordCount',
      width: 80,
      render: (count) => <Text strong>{count}</Text>,
    },
    {
      title: '今日新增',
      dataIndex: 'todayCount',
      width: 80,
      render: (count) => (
        <Text type={count > 0 ? 'success' : 'secondary'}>
          +{count}
        </Text>
      ),
    },
  ]

  const isAdmin = session?.user?.role === 'admin'

  return (
    <div className="space-y-6">
      {/* 欢迎信息 */}
      <div>
        <Title level={2} className="mb-2">
          欢迎回来，{session?.user?.nickname || session?.user?.username}
        </Title>
        <Text type="secondary">
          {dayjs().format('YYYY年MM月DD日 dddd')} - 
          {isAdmin ? ' 管理员面板' : ' 用户面板'}
        </Text>
      </div>

      {/* 刷新按钮 */}
      <div className="flex justify-end">
        <Button
          icon={<ReloadOutlined />}
          onClick={fetchDashboardData}
          loading={loading}
        >
          刷新数据
        </Button>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        {isAdmin && (
          <>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="用户总数"
                  value={stats.totalUsers}
                  prefix={<UserOutlined />}
                  suffix={`/ ${stats.activeUsers} 活跃`}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="表单配置"
                  value={stats.totalForms}
                  prefix={<FormOutlined />}
                  suffix={`/ ${stats.activeForms} 启用`}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
          </>
        )}
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="数据总量"
              value={stats.totalRecords}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日新增"
              value={stats.todayRecords}
              prefix={<RiseOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Webhook 状态 */}
      {isAdmin && (
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card 
              title={
                <Space>
                  <LinkOutlined />
                  <span>Webhook 状态</span>
                </Space>
              }
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic
                    title="成功接收"
                    value={stats.webhookSuccess}
                    valueStyle={{ color: '#52c41a' }}
                    prefix={<CheckCircleOutlined />}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="接收错误"
                    value={stats.webhookErrors}
                    valueStyle={{ color: '#ff4d4f' }}
                    prefix={<ExclamationCircleOutlined />}
                  />
                </Col>
              </Row>
              <div className="mt-4">
                <div className="flex justify-between items-center mb-2">
                  <Text type="secondary">成功率</Text>
                  <Text strong>
                    {stats.webhookSuccess + stats.webhookErrors > 0 
                      ? Math.round((stats.webhookSuccess / (stats.webhookSuccess + stats.webhookErrors)) * 100)
                      : 100
                    }%
                  </Text>
                </div>
                <Progress
                  percent={
                    stats.webhookSuccess + stats.webhookErrors > 0 
                      ? Math.round((stats.webhookSuccess / (stats.webhookSuccess + stats.webhookErrors)) * 100)
                      : 100
                  }
                  strokeColor="#52c41a"
                  size="small"
                />
              </div>
            </Card>
          </Col>
          
          <Col xs={24} lg={12}>
            <Card 
              title={
                <Space>
                  <TrophyOutlined />
                  <span>表单排行</span>
                </Space>
              }
            >
              <Table
                columns={formColumns}
                dataSource={formStats.slice(0, 5)}
                rowKey="formId"
                pagination={false}
                size="small"
              />
              {formStats.length > 5 && (
                <div className="text-center mt-3">
                  <Link href="/forms">
                    <Button type="link" size="small">
                      查看全部 {formStats.length} 个表单
                    </Button>
                  </Link>
                </div>
              )}
            </Card>
          </Col>
        </Row>
      )}

      {/* 最近活动 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={isAdmin ? 16 : 24}>
          <Card 
            title={
              <Space>
                <ClockCircleOutlined />
                <span>最近活动</span>
              </Space>
            }
            extra={
              isAdmin && (
                <Link href="/settings/logs">
                  <Button type="link" size="small">
                    查看全部日志
                  </Button>
                </Link>
              )
            }
          >
            <List
              size="small"
              dataSource={recentActivities}
              renderItem={(item) => (
                <List.Item>
                  <div className="w-full flex justify-between items-center">
                    <div className="flex items-center space-x-3">
                      <Tag 
                        color={actionMap[item.action]?.color || 'default'}
                        className="min-w-0"
                      >
                        {actionMap[item.action]?.label || item.action}
                      </Tag>
                      <div className="flex-1 min-w-0">
                        <Text ellipsis className="text-sm">
                          {item.user ? 
                            `${item.user.nickname} 执行了操作` : 
                            '系统自动执行'
                          }
                        </Text>
                        {item.details?.formName && (
                          <div>
                            <Text type="secondary" className="text-xs">
                              表单: {item.details.formName}
                            </Text>
                          </div>
                        )}
                      </div>
                    </div>
                    <Text type="secondary" className="text-xs whitespace-nowrap ml-2">
                      {dayjs(item.createdAt).fromNow()}
                    </Text>
                  </div>
                </List.Item>
              )}
            />
            {recentActivities.length === 0 && (
              <div className="text-center py-8">
                <Text type="secondary">暂无活动记录</Text>
              </div>
            )}
          </Card>
        </Col>

        {isAdmin && (
          <Col xs={24} lg={8}>
            <Card 
              title={
                <Space>
                  <ExclamationCircleOutlined />
                  <span>系统提醒</span>
                </Space>
              }
            >
              <div className="space-y-3">
                {stats.webhookErrors > 0 && (
                  <Alert
                    message="Webhook错误"
                    description={`检测到 ${stats.webhookErrors} 个Webhook接收错误，请检查配置`}
                    type="error"
                    showIcon
                    size="small"
                  />
                )}
                
                {stats.activeForms === 0 && (
                  <Alert
                    message="无活跃表单"
                    description="当前没有启用的表单配置，请创建表单配置"
                    type="warning"
                    showIcon
                    size="small"
                    action={
                      <Link href="/forms/config">
                        <Button size="small" type="text">
                          立即创建
                        </Button>
                      </Link>
                    }
                  />
                )}

                {stats.todayRecords === 0 && stats.activeForms > 0 && (
                  <Alert
                    message="今日无新数据"
                    description="今天还没有接收到新的表单数据"
                    type="info"
                    showIcon
                    size="small"
                  />
                )}

                {stats.webhookErrors === 0 && stats.activeForms > 0 && stats.todayRecords > 0 && (
                  <Alert
                    message="系统运行正常"
                    description="所有服务运行正常，数据接收正常"
                    type="success"
                    showIcon
                    size="small"
                  />
                )}
              </div>
            </Card>
          </Col>
        )}
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={6}>
            <Link href="/data/view">
              <Card hoverable size="small" className="text-center">
                <DatabaseOutlined className="text-2xl text-blue-500 mb-2" />
                <div>查看数据</div>
              </Card>
            </Link>
          </Col>
          {isAdmin && (
            <>
              <Col xs={24} sm={12} md={6}>
                <Link href="/forms/config">
                  <Card hoverable size="small" className="text-center">
                    <FormOutlined className="text-2xl text-green-500 mb-2" />
                    <div>新建表单</div>
                  </Card>
                </Link>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Link href="/settings/users">
                  <Card hoverable size="small" className="text-center">
                    <UserOutlined className="text-2xl text-purple-500 mb-2" />
                    <div>用户管理</div>
                  </Card>
                </Link>
              </Col>
            </>
          )}
          <Col xs={24} sm={12} md={6}>
            <Link href="/settings/profile">
              <Card hoverable size="small" className="text-center">
                <UserOutlined className="text-2xl text-orange-500 mb-2" />
                <div>个人设置</div>
              </Card>
            </Link>
          </Col>
        </Row>
      </Card>
    </div>
  )
}