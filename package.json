{"name": "lung-function-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "test": "jest", "test:watch": "jest --watch", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "packageManager": "yarn@1.22.0", "dependencies": {"@ant-design/icons": "^5.2.6", "@prisma/client": "^5.7.1", "antd": "^5.12.8", "bcryptjs": "^2.4.3", "clsx": "^2.0.0", "dayjs": "^1.11.13", "next": "14.2.5", "next-auth": "^4.24.7", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.48.2", "tailwind-merge": "^2.2.0", "winston": "^3.11.0", "xlsx": "^0.18.5", "zod": "^3.25.67", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.6", "@testing-library/react": "^14.1.2", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.2.5", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "prisma": "^5.7.1", "tailwindcss": "^3.4.0", "tsx": "^4.7.0", "typescript": "^5.3.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}