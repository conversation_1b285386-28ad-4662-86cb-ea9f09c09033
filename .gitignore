# Dependencies
node_modules/
/.pnp
.pnp.js

# Testing
/coverage

# Next.js
/.next/
/out/

# Production
/build
/dist

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env*.local
.env
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDEs
.vscode/
.idea/
*.swp
*.swo

# Logs
logs/
*.log

# Database
/prisma/dev.db
/prisma/dev.db-journal

# Uploads
/uploads/
/temp/

# Lock files
yarn.lock
package-lock.json

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp

# Documentation
/docs/build/

# Coverage directory used by tools like istanbul
coverage/

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# Husky
.husky/_